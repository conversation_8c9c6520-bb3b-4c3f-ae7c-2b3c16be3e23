#!/usr/bin/env pwsh
# Script to sync ArgoCD application after configuration changes

param(
    [string]$AppName = "ai-react-frontend",
    [string]$ArgoNamespace = "argocd",
    [switch]$Force,
    [switch]$WaitForSync,
    [int]$TimeoutSeconds = 300
)

Write-Host "🔄 Syncing ArgoCD application: $AppName" -ForegroundColor Cyan
Write-Host ""

# Function to check if kubectl is available and configured
function Test-KubectlAccess {
    try {
        $currentContext = kubectl config current-context 2>$null
        if ($currentContext) {
            Write-Host "✅ kubectl configured with context: $currentContext" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ kubectl not configured or no current context" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ kubectl not available: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to check ArgoCD application status
function Get-ArgoCDApplicationStatus {
    try {
        $app = kubectl get application $AppName -n $ArgoNamespace -o json 2>$null | ConvertFrom-Json
        if ($app) {
            return @{
                SyncStatus = $app.status.sync.status
                HealthStatus = $app.status.health.status
                Revision = $app.status.sync.revision
                Message = $app.status.conditions[-1].message
            }
        } else {
            return $null
        }
    } catch {
        Write-Host "❌ Error getting application status: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Function to trigger ArgoCD sync
function Invoke-ArgoCDSync {
    param([bool]$ForceSync = $false)
    
    try {
        if ($ForceSync) {
            Write-Host "🔄 Triggering force sync..." -ForegroundColor Yellow
            kubectl patch application $AppName -n $ArgoNamespace --type merge -p '{"operation":{"sync":{"syncStrategy":{"force":true}}}}'
        } else {
            Write-Host "🔄 Triggering regular sync..." -ForegroundColor Yellow
            kubectl patch application $AppName -n $ArgoNamespace --type merge -p '{"operation":{"sync":{}}}'
        }
        
        Start-Sleep -Seconds 2
        return $true
    } catch {
        Write-Host "❌ Failed to trigger sync: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to wait for sync completion
function Wait-ForSyncCompletion {
    param([int]$Timeout = 300)
    
    Write-Host "⏳ Waiting for sync to complete (timeout: ${Timeout}s)..." -ForegroundColor Yellow
    
    $elapsed = 0
    $interval = 10
    
    do {
        Start-Sleep -Seconds $interval
        $elapsed += $interval
        
        $status = Get-ArgoCDApplicationStatus
        if ($status) {
            $syncStatus = $status.SyncStatus
            $healthStatus = $status.HealthStatus
            
            Write-Host "[$elapsed/${Timeout}s] Sync: $syncStatus | Health: $healthStatus" -ForegroundColor Gray
            
            if ($syncStatus -eq "Synced") {
                if ($healthStatus -eq "Healthy") {
                    Write-Host "✅ Application is synced and healthy!" -ForegroundColor Green
                    return $true
                } elseif ($healthStatus -eq "Progressing") {
                    Write-Host "🔄 Application is synced but still progressing..." -ForegroundColor Yellow
                    # Continue waiting for health to become healthy
                } else {
                    Write-Host "⚠️  Application is synced but health status is: $healthStatus" -ForegroundColor Yellow
                    # Continue waiting a bit more
                }
            } elseif ($syncStatus -eq "OutOfSync") {
                Write-Host "🔄 Application is out of sync, continuing to wait..." -ForegroundColor Yellow
            } else {
                Write-Host "🔄 Sync status: $syncStatus, continuing to wait..." -ForegroundColor Yellow
            }
        } else {
            Write-Host "❌ Could not get application status" -ForegroundColor Red
        }
        
    } while ($elapsed -lt $Timeout)
    
    Write-Host "⚠️  Timeout reached. Final status check..." -ForegroundColor Yellow
    $finalStatus = Get-ArgoCDApplicationStatus
    if ($finalStatus) {
        Write-Host "Final Status - Sync: $($finalStatus.SyncStatus) | Health: $($finalStatus.HealthStatus)" -ForegroundColor Gray
        return ($finalStatus.SyncStatus -eq "Synced" -and $finalStatus.HealthStatus -eq "Healthy")
    }
    
    return $false
}

# Function to display application information
function Show-ApplicationInfo {
    $status = Get-ArgoCDApplicationStatus
    if ($status) {
        Write-Host "📊 Current Application Status:" -ForegroundColor Cyan
        Write-Host "  Sync Status: $($status.SyncStatus)" -ForegroundColor $(if ($status.SyncStatus -eq "Synced") { "Green" } else { "Red" })
        Write-Host "  Health Status: $($status.HealthStatus)" -ForegroundColor $(if ($status.HealthStatus -eq "Healthy") { "Green" } else { "Red" })
        Write-Host "  Revision: $($status.Revision)" -ForegroundColor Gray
        if ($status.Message) {
            Write-Host "  Message: $($status.Message)" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ Could not retrieve application status" -ForegroundColor Red
    }
}

# Main execution
Write-Host "Application: $AppName" -ForegroundColor Gray
Write-Host "Namespace: $ArgoNamespace" -ForegroundColor Gray
Write-Host "Force Sync: $Force" -ForegroundColor Gray
Write-Host "Wait for Sync: $WaitForSync" -ForegroundColor Gray
Write-Host ""

# Check prerequisites
if (-not (Test-KubectlAccess)) {
    Write-Host "❌ Cannot proceed without kubectl access to the management cluster" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 Alternative approaches:" -ForegroundColor Yellow
    Write-Host "1. Use ArgoCD UI to manually sync the application" -ForegroundColor White
    Write-Host "2. Configure kubectl to access the management cluster" -ForegroundColor White
    Write-Host "3. Use ArgoCD CLI if available" -ForegroundColor White
    exit 1
}

# Show current status
Write-Host "📊 Checking current application status..." -ForegroundColor Yellow
Show-ApplicationInfo
Write-Host ""

# Trigger sync
$syncSuccess = Invoke-ArgoCDSync -ForceSync $Force

if (-not $syncSuccess) {
    Write-Host "❌ Failed to trigger sync" -ForegroundColor Red
    exit 1
}

# Wait for completion if requested
if ($WaitForSync) {
    $completed = Wait-ForSyncCompletion -Timeout $TimeoutSeconds
    
    if ($completed) {
        Write-Host "✅ Sync completed successfully!" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Sync may still be in progress or encountered issues" -ForegroundColor Yellow
    }
} else {
    Write-Host "🔄 Sync triggered. Check ArgoCD UI for progress." -ForegroundColor Yellow
    Start-Sleep -Seconds 5
}

# Show final status
Write-Host ""
Write-Host "📊 Final Application Status:" -ForegroundColor Cyan
Show-ApplicationInfo

Write-Host ""
Write-Host "🎯 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Monitor ArgoCD UI: Check the application dashboard" -ForegroundColor White
Write-Host "2. Check pods: kubectl get pods -n ai-react-frontend-dev" -ForegroundColor White
Write-Host "3. View logs: kubectl logs -f deployment/ai-react-frontend -n ai-react-frontend-dev" -ForegroundColor White
Write-Host "4. Test connectivity: kubectl port-forward svc/ai-react-frontend 8080:80 -n ai-react-frontend-dev" -ForegroundColor White

Write-Host ""
Write-Host "📚 Useful Commands:" -ForegroundColor Yellow
Write-Host "# Check application details" -ForegroundColor Gray
Write-Host "kubectl describe application $AppName -n $ArgoNamespace" -ForegroundColor Gray
Write-Host ""
Write-Host "# Manual sync via kubectl" -ForegroundColor Gray
Write-Host "kubectl patch application $AppName -n $ArgoNamespace --type merge -p '{\"operation\":{\"sync\":{}}}'" -ForegroundColor Gray
