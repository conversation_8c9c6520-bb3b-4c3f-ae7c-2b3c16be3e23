apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
metadata:
  name: ai-react-frontend-dev
  annotations:
    config.kubernetes.io/local-config: "true"
namespace: ai-react-frontend-dev
resources:
  - ../../base
  - hpa.yaml
  - pdb.yaml
patches:
  - path: deployment-patch.yaml
    target:
      kind: Deployment
      name: ai-react-frontend
commonLabels:
  environment: dev
  deployment-strategy: rolling-fast
commonAnnotations:
  deployment.kubernetes.io/environment: "dev"
  deployment.kubernetes.io/strategy: "rolling-fast"
  deployment.kubernetes.io/cluster: "6be4e15d-52f9-431d-84ec-ec8cad0dff2d"
# Development Environment Configuration
replicas:
  - name: ai-react-frontend
    count: 2  # Minimum for rolling updates
# Development-specific image configuration
images:
  - name: app-image
    newName: saipriya104/ai-react-frontend
    newTag: latest
# Development environment uses base ConfigMap and Secret resources
# No additional generators needed - base resources are sufficient
